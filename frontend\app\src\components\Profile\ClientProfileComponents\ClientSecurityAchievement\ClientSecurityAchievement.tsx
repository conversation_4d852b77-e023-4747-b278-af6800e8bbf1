// @ts-nocheck
import React from 'react';
import { Card, Text, View } from 'reshaped';
import InfoTooltip from 'src/components/common/InfoTooltip';
import { ClientType } from '../../../../store/dummydata/OperatorsDummyData';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';
interface ClientSecurityAchievementProps {
  oper: any;
}
interface SecurityCheck {
  name: string;
  value: boolean;
  additionalData: number[];
}
const ClientSecurityAchievement: React.FC<ClientSecurityAchievementProps> = ({
  oper,
}) => {
  const { sia_certificates } = oper;

  const securityChecks: SecurityCheck[] = [
    {
      name: 'SIA licence',
      value: sia_certificates?.[0]?.sia_licence,
      additionalData: [],
    },
    {
      name: 'ID check',
      value: sia_certificates?.[0]?.id_check,
      additionalData: [],
    },
    {
      name: 'Proof of address',
      value: sia_certificates?.[0]?.proof_of_address,
      additionalData: [],
    },
    {
      name: 'Employment history',
      value: sia_certificates?.[0]?.employment_history,
      additionalData: [],
    },
    {
      name: 'Credit check',
      value: sia_certificates?.[0]?.credit_check,
      additionalData: [],
    },
    {
      name: 'No criminal record',
      value: sia_certificates?.[0]?.no_criminal_record,
      additionalData: [],
    },
  ];
  return (
    <Card className='xl:w-full h-[auto] xl:mx-auto p-6 xl:max-w-[200px]'>
      <View className='flex items-center justify-between'>
        <View className='flex gap-2 items-center'>
          <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>
            BS7858
          </Text>

          <InfoTooltip text='BS7858 it is a rigorous screening standard that ensures the highest level of professionalism and integrity in the security industry. This standard covers comprehensive background checks, including identity verification, employment history and criminal record checks. We conduct internal verification to ensure the validity of the SIA license on some details, while other information are self-verified by security operatives.' />
        </View>
      </View>
      <View className='mt-[20px] flex flex-col gap-3'>
        {securityChecks.length === 0 ? (
          <NoDataClientProfile />
        ) : (
          securityChecks.map((check, index) => (
            <View key={index} className='flex items-center gap-2'>
              <span
                className={`material-icons-outlined text-[14px] ${
                  check.value ? 'text-[#05751F]' : 'text-[#BABABC]'
                }`}
              >
                {check.value ? 'check_circle' : 'cancel'}
              </span>
              <Text
                className={`text-neutral rubik text-xs font-medium leading-5 ${
                  check.value ? 'text-[#05751F]' : 'text-[#BABABC]'
                }`}
              >
                {check.name}
              </Text>
            </View>
          ))
        )}
      </View>
    </Card>
  );
};

export default ClientSecurityAchievement;
