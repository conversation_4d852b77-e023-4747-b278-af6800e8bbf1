import React from 'react';
import { Button } from 'reshaped';

interface EditButtonProps {
  onClick: () => void;
  iconName?: string;
  size?: 'small' | 'medium';
}

/**
 * EditButton - A standardized edit button with properly centered icon
 * 
 * @param {Function} onClick - The function to call when the button is clicked
 * @param {string} iconName - Optional icon name to use instead of 'edit'
 * @param {string} size - Size of the button (small or medium)
 */
const EditButton: React.FC<EditButtonProps> = ({ onClick, iconName = 'edit', size = 'medium' }) => {
  // Determine size classes based on the size prop
  const buttonSize = size === 'small' ? '!w-[24px] !h-[24px]' : '!w-[32px] !h-[32px]';
  const iconSize = size === 'small' ? '!text-[20px]' : '!text-[22px]';
  
  return (
    <Button
      onClick={onClick}
      className={`${buttonSize} !rounded-full border border-[#323C58] !bg-[#323C58] !p-0 flex items-center justify-center`}
    >
      <span 
        className={`material-icons-outlined text-white ${iconSize} !leading-none flex items-center justify-center`}
        style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        {iconName}
      </span>
    </Button>
  );
};

export default EditButton;
