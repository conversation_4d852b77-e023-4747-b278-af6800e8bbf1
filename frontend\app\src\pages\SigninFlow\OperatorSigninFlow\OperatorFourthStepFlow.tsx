// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Button, Text, View, Image, useToggle, useToast } from 'reshaped';
import { useNavigate } from 'react-router-dom';

import { useRegistrationContext } from 'src/context/RegistrationContext';

import { validateOperatorProfile } from 'src/services/user';
import { headerLogo } from '../../../assets/images';
import { useModalAction } from 'src/context/ModalContext';
import { AppContext } from 'src/context/AppContext';
import CloseAccountCreatorModal from '../CloseAccountCreatorModal/CloseAccountCreatorModal';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

const OperatorFourthStepFlow: React.FC = () => {
  const { openModal } = useModalAction();
  const { fetchAppData } = useContext(AppContext);
  const { active, activate, deactivate } = useToggle(false);
  const toast = useToast();

  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImageSelfie, setCapturedImageSelfie] = useState<string | null>(null);
  const navigate = useNavigate();

  const [validSelfie, setValidSelfie] = useState(true);

  const { sharedRegisterData, operatorRegisterData, setOperatorRegisterData } = useRegistrationContext();

  const openCamera = async () => {
    try {
      const cameraStream = await navigator.mediaDevices.getUserMedia({
        video: true,
      });
      setStream(cameraStream);
      setIsCameraOpen(true);
    } catch (error) {
      toast.show({
        title: 'Camera Error',
        text: 'Unable to access your camera. Please check your permissions and try again.',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
      });
    }
  };

  const closeCamera = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    setStream(null);
    setIsCameraOpen(false);
  };

  const takePicture = () => {
    if (stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      const canvas = document.createElement('canvas');
      
      // Use a smaller max width for better compression
      const maxWidth = 640; // Reduced from 800 to 640
      const scaleFactor = maxWidth / videoElement.videoWidth;
      canvas.width = maxWidth;
      canvas.height = videoElement.videoHeight * scaleFactor;
      
      const context = canvas.getContext('2d');
      if (context) {
        // Apply some image smoothing for better compression
        context.imageSmoothingEnabled = true;
        context.imageSmoothingQuality = 'medium';
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        
        // Determine appropriate quality based on initial test compression
        // First try with medium quality
        let quality = 0.6; // Start with 60% quality
        let capturedDataURL = canvas.toDataURL('image/jpeg', quality);
        let imageSizeInBytes = capturedDataURL.length;
        
        // If still too large, compress further
        if (imageSizeInBytes > 3000000) { // If over 3MB, compress more
          quality = 0.4; // Reduce to 40% quality
          capturedDataURL = canvas.toDataURL('image/jpeg', quality);
          imageSizeInBytes = capturedDataURL.length;
          
          // If still too large, compress even further
          if (imageSizeInBytes > 2000000) { // If still over 2MB
            quality = 0.3; // Reduce to 30% quality
            capturedDataURL = canvas.toDataURL('image/jpeg', quality);
            imageSizeInBytes = capturedDataURL.length;
          }
        }
        
        // Calculate size in MB for display
        const imageSizeInMB = imageSizeInBytes / (1024 * 1024);
        
        // Check if the image is still too large
        if (imageSizeInBytes > 5000000) {
          // Show warning for large file size
          toast.show({
            title: 'Warning: Large Image',
            text: `Your selfie is ${imageSizeInMB.toFixed(1)}MB, which exceeds the 5MB limit. Try adjusting lighting or taking the photo in a brighter area.`,
            icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          });
        } else {
          // Show success message with size information
          setCapturedImageSelfie(capturedDataURL);
          const id1 = toast.show({
            title: 'Picture Saved Successfully',
            text: `Your picture has been saved (${imageSizeInMB.toFixed(1)}MB).`,
            icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
            startSlot: <Button variant='ghost' className='h-[21px] w-[21px]' onClick={() => toast.hide(id1)}><Text className='text-[12px]'>X</Text></Button>,
          });
        }
      }
      setValidSelfie(true);
    }
  };

  useEffect(() => {
    if (isCameraOpen && stream) {
      const videoElement = document.getElementById('camera-feed') as HTMLVideoElement;
      videoElement.srcObject = stream;
    }
  }, [isCameraOpen, stream]);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleSubmit = async () => {
    try {
      // Step 1: Update registration data with selfie
      setOperatorRegisterData((prevState: any) => ({
        ...prevState,
        fourthStep: {
          capturedImageSelfie: capturedImageSelfie,
        },
      }));

      // Step 2: Validate selfie exists
      if (!capturedImageSelfie) {
        return setValidSelfie(false);
      } else setValidSelfie(true);

      // Step 3: Check if required data exists
      if (!sharedRegisterData?.baseData) {
        const errorCode = 'ERR_MISSING_BASE_DATA';
        toast.show({
          title: 'Missing Data Error',
          text: `Missing basic registration data. Please restart the registration process. (Error code: ${errorCode})`,
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        });
        return;
      }
      
      if (!sharedRegisterData?.crucialData) {
        const errorCode = 'ERR_MISSING_CRUCIAL_DATA';
        toast.show({
          title: 'Missing Data Error',
          text: `Missing crucial verification data. Please restart the registration process. (Error code: ${errorCode})`,
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        });
        return;
      }

      // Step 4: Check for previous steps data
      if (!operatorRegisterData?.firstStep || !operatorRegisterData?.secondStep || !operatorRegisterData?.thirdStep) {
        const missingSteps = [];
        if (!operatorRegisterData?.firstStep) missingSteps.push('first');
        if (!operatorRegisterData?.secondStep) missingSteps.push('second');
        if (!operatorRegisterData?.thirdStep) missingSteps.push('third');
        
        const errorCode = 'ERR_INCOMPLETE_REGISTRATION';
        toast.show({
          title: 'Incomplete Registration',
          text: `Missing data from ${missingSteps.join(', ')} step(s). Please complete all steps. (Error code: ${errorCode})`,
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        });
        return;
      }

      const baseData = sharedRegisterData.baseData;
      const crucialData = sharedRegisterData.crucialData;

      const operatorRegisterFinalData = {
        ...baseData,
        ...crucialData,
        ...operatorRegisterData?.firstStep,
        ...operatorRegisterData?.secondStep,
        ...operatorRegisterData?.thirdStep,
        capturedImageSelfie,
      };

      // Step 5: Show loading state
      const loadingToast = toast.show({
        title: 'Processing',
        text: 'Submitting your verification data...',
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
      });

      // Step 6: Submit data
      try {
        let response = await validateOperatorProfile(operatorRegisterFinalData);
        toast.hide(loadingToast);

        if (response && response.success) {
          // Success path
          closeCamera();
          fetchAppData();
          navigate('/my-profile');
        } else {
          // API returned but with error
          const errorCode = response?.errorCode || 'ERR_API_RESPONSE';
          const errorMessage = response?.message || 'There was a problem submitting your verification.';
          
          toast.show({
            title: 'Submission Failed',
            text: `${errorMessage} (Error code: ${errorCode})`,
            icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
          });
        }
      } catch (apiError: any) {
        toast.hide(loadingToast);
        
        // Handle specific API errors
        const errorCode = 'ERR_API_CALL_FAILED';
        const statusCode = apiError?.response?.status || 'unknown';
        const errorDetail = apiError?.response?.data?.message || apiError?.message || 'Unknown error';
        
        toast.show({
          title: 'API Error',
          text: `Failed to submit verification: ${errorDetail}. Status: ${statusCode}. Please contact support with this error code: ${errorCode}`,
          icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
        });
      }
    } catch (error: any) {
      // Catch-all for unexpected errors
      const errorCode = 'ERR_UNEXPECTED';
      const errorMessage = error?.message || 'Unknown error occurred';
      
      toast.show({
        title: 'Unexpected Error',
        text: `An unexpected error occurred: ${errorMessage}. Please contact support with this error code: ${errorCode}`,
        icon: <Image src={surleyicon} className='h-[20px] w-[20px]' />,
      });
    }
  };

  return (
    <View className='mt-[20px] flex flex-col overflow-hidden px-[12px] sm:mt-[84.3px] md:px-0'>
      <View className='flex w-full items-end justify-end bg-[C85E1D]'>
        <Button variant='ghost' onClick={activate} className='btn-no-hover ' icon={() => <span className='material-icons mt-[-3px]'>close</span>} />
      </View>
      <CloseAccountCreatorModal active={active} deactivate={deactivate} />

      <View className='mx-auto flex  flex-col sm:w-[536px]'>
        <Text className='font-rufina-stencil leading-40 text-[32px] font-normal text-[#1A1A1A] sm:text-center'>Take a selfie for verification</Text>
        <Text className='rubik mx-auto mt-[16px] text-base font-normal leading-6 text-[#323C58] sm:text-center lg:w-[488px]'>
          To confirm your identity, use your camera to capture a photograph of yourself. This image will not appear on your profile.
        </Text>
        <Text className='rubik mx-auto mt-[8px] text-sm font-normal text-[#666] sm:text-center'>
          <span className='block'>Note: Image must be under 5MB in size.</span>
          <span className='block mt-1 font-semibold text-[#E74C3C]'>Important: The total size of all verification images combined must be under 15MB.</span>
        </Text>
        <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#1A1A1A] '>Use your camera to take a picture of yourself.</Text>
        <View className='mt-[16px] flex flex-row'>
          <Button className='btn-no-hover_one mr-[12px] mt-[10px] flex h-8 w-8 items-center justify-center rounded-[100px] !bg-[#DFE2EA] sm:mt-[0px] sm:h-16 sm:w-16'>
            <span className='material-icons-outlined mt-[-4px] align-middle text-black'>person</span>
          </Button>
          <View className='w-full sm:w-auto'>
            <Button
              variant='outline'
              icon={() => <span className='material-icons-outlined mr-[8px] mt-[-1px] text-[20px]'>photo_camera</span>}
              onClick={isCameraOpen ? closeCamera : openCamera}
              className='flex h-[60px] w-full rounded-[8px] !border-[#DFE2EA] !bg-[#FFFF] sm:w-[412px]'
            >
              <Text className='rubik text-[15px] font-medium leading-[20px]'> {isCameraOpen ? 'Close Camera' : 'Take a picture'}</Text>
            </Button>
            {isCameraOpen && (
              <Button
                variant='outline'
                icon={() => <span className='material-icons-outlined mr-[8px] mt-[-2px] text-[20px]'>photo</span>}
                onClick={takePicture}
                className='mt-[10px] flex h-[60px] rounded-[8px] !border-[#DFE2EA] !bg-[#FFFF] sm:w-[412px]'
              >
                <Text className='rubik text-[15px] font-medium leading-[20px]'>Take Picture</Text>
              </Button>
            )}
            <View className='flex flex-row items-center justify-between'>
              {isCameraOpen && (
                <div className='flex items-center justify-center'>
                  <video id='camera-feed' className='h-[230px] w-[230px]' autoPlay playsInline></video>
                </div>
              )}
              {capturedImageSelfie && (
                <div className='flex h-[230px] w-[230px] items-center justify-center'>
                  <img src={capturedImageSelfie} alt='Captured' />
                </div>
              )}
            </View>
          </View>
        </View>
        {!validSelfie && <Text className='rubik mx-auto mt-[16px] text-[15px] font-normal  leading-5 text-red-400'>Selfie picture is required.</Text>}
      </View>

      <View className='mt-[20px] flex  flex-col sm:mt-[123px] xl:w-[1320px]'>
        <div className='flex h-[6px] w-full'>
          <div className='h-full w-full bg-[#0B80E7]' />
        </div>
        <View className='mt-[16px] flex flex-row justify-between'>
          <Button
            icon={() => <span className='material-icons-outlined text-[19px] text-[#14171F]'>arrow_back_ios</span>}
            onClick={handleGoBack}
            className='bg-background-base flex h-[48px] items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border border-[#DFE2EA] !bg-[white]  px-4  py-2 sm:w-[103px]'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#14171F]'>Back</Text>
          </Button>

          <Button
            endIcon={() => <span className='material-icons-outlined text-[18px] text-[#FFF]'>arrow_forward_ios</span>}
            onClick={handleSubmit}
            className='border-neutral bg-background-base flex h-[48px] items-center justify-center self-stretch self-stretch rounded-[8px] border !bg-[#0B80E7] sm:w-[135px] relative z-10'
          >
            <Text className='rubik mt-[3px] text-[16px] font-medium leading-[24px] text-[#FFF]'>Submit</Text>
          </Button>
        </View>
        <div className='mt-[30px] flex items-center justify-center sm:mt-[0px]'>
          <Image src={headerLogo} className='h-[41.274px] w-[109.76px] flex-shrink-0' />
        </div>
      </View>
    </View>
  );
};

export default OperatorFourthStepFlow;
