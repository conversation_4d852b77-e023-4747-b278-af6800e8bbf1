// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, Text, Button, View, Image } from 'reshaped';
import InfoTooltip from 'src/components/common/InfoTooltip';
import { useNavigate } from 'react-router-dom';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';

interface ClientBioProps {
  oper: any;
}
const ClientBio: React.FC<ClientBioProps> = ({ oper }) => {
  const { profile_description, profile_title } = oper;

  return (
    <Card className='p-[24px] lg:w-[426px]  xl:mx-auto xl:w-[536px]'>
      <View className='flex items-center justify-between'>
        <div className='flex w-full justify-between'>
          <div className='flex items-center gap-2'>
            <Text className='rubik text-center text-[16px] text-black xl:font-medium xl:leading-5'>Bio</Text>

            <InfoTooltip text='On your profile page, you have the opportunity to showcase your personality through a bio. The minimum number of characters allowed for your bio is 20. The maximum limit is set at 600 characters, encouraging concise and impactful self-introductions.' />
          </div>
        </div>
      </View>
      {profile_title !== null || profile_description !== null ? (
        <View className='mt-[20px] flex flex-col gap-2 xl:w-[470px]'>
          <Text className='text-neutral rubik ml-[4px] break-all text-lg font-medium leading-7 '>{profile_title}</Text>
          <Text className='text-light-gray rubik xl:text-body2 w-full xl:font-normal xl:leading-5 '>{profile_description}</Text>
        </View>
      ) : (
        <NoDataClientProfile />
      )}
    </Card>
  );
};

export default ClientBio;
