<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Contract;
use Carbon\Carbon;

class UpdateContractStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'contracts:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the status of contracts to "complete" if they are paid and their end date has passed.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking for contracts to update...');

        $contracts = Contract::where('payment_status', Contract::PAYMENT_STATUS_PAID)
            ->where('status', '!=', Contract::complete)
            ->where('end_date', '<', Carbon::now())
            ->get();

        if ($contracts->isEmpty()) {
            $this->info('No contracts to update.');
            return 0;
        }

        foreach ($contracts as $contract) {
            $contract->status = Contract::complete;
            $contract->save();
            $this->info("Contract #{$contract->id} has been marked as complete.");
        }

        $this->info('Contract status updates complete.');
        return 0;
    }
}