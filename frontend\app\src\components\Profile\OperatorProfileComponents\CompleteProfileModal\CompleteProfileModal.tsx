// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import {
  Text,
  View,
  Button,
  Divider,
  Modal,
  Progress,
} from 'reshaped';
import InfoTooltip from 'src/components/common/InfoTooltip';
import { useNavigate } from 'react-router-dom';
import { AppContext } from 'src/context/AppContext';

interface CompleteProfileModalProps {
  active: boolean;
  deactivate: () => void;
  onCompletenessChange: (value: number) => void;
}

const CompleteProfileModal: React.FC<CompleteProfileModalProps> = ({
  active,
  deactivate,
  onCompletenessChange,
}) => {
  const navigate = useNavigate();
  const {
    positions,
    profileTitle,
    profileDescription,
    siaLicense,
    languages,
    testlang,
    qualifications,
    industrySectors,
    profileVideo,
    additionalPictures,
    profilePhoto,
  } = useContext(AppContext);
  // const [industrySectors, setIndustrySectors] = useState<string[]>([]);
  // const [profilePhoto, setProfilePhoto] = useState<string | undefined>();
  // const [profileVideo, setProfileVideo] = useState<string | undefined>();
  // const [SIALicense, setSIALicense] = useState([]);
  // const [additionalPictures, setAdditionalPictures] = useState<string[]>([]);
  // const [languages, setLanguages] = useState<LanguageData[]>([]);
  // const [testlang, settestlang] = useState<any>([]);
  const [completenessValue, setCompletenessValue] = useState<number>(20);

  const completeProfileValues = [
    {
      value: positions,
      heading: 'Employment history: +10%',
      subHeading: 'Fill your eployment history',
      link: '/operator-settings-employment',
      activeTab: '0',
    },
    {
      value: profileTitle && profileDescription,
      heading: 'Profile title and description: +10%',
      subHeading: 'Fill your profile title and description',
      link: '/operator-settings-profile-details',
      activeTab: '2',
    },
    {
      value: testlang,
      heading: 'Languages: +10%',
      subHeading: 'Fill your languages',
      link: '/operator-settings-profile-details',
      activeTab: '3',
    },
    {
      value: qualifications,
      heading: 'Relevant qualifications: +10%',
      subHeading: 'Fill your relevant qualifications',
      link: '/operator-settings-employment',
      activeTab: '1',
    },
    {
      value: industrySectors,
      heading: 'Industry sectors: +10%',
      subHeading: 'Fill your industry sectors',
      link: '/operator-settings-profile-details',
      activeTab: '0',
    },
    {
      value: profileVideo,
      heading: 'Video introduction: +10%',
      subHeading: 'Record your introduction video',
      link: '/operator-settings-profile-details',
      activeTab: '1',
    },
    {
      value: additionalPictures,
      heading: 'Additional pictures: +10%',
      subHeading: 'Fill your additional pictures',
      link: '/operator-settings-profile-details',
      activeTab: '1',
    },
    {
      value: profilePhoto,
      heading: 'Profile Photo: +10%',
      subHeading: 'Fill your Profile Photo',
      link: '/operator-settings-profile-details',
      activeTab: '1',
    },
  ];

  const sortedValues = completeProfileValues.sort((a, b) => {
    if (
      (a.value == null || a.value == undefined || a.value.length === 0) &&
      (b.value == null || b.value == undefined || b.value.length === 0)
    ) {
      return 0;
    }
    if (a.value == null || a.value == undefined || a.value.length === 0) {
      return -1;
    }
    if (b.value == null || b.value == undefined || b.value.length === 0) {
      return 1;
    }
    return a.value - b.value;
  });

  useEffect(() => {
    let newCompletenessValue = 20;

    if (positions?.length > 0) {
      newCompletenessValue += 10;
    }
    if (qualifications?.length > 0) {
      newCompletenessValue += 10;
    }
    if (profileVideo !== undefined) {
      newCompletenessValue += 10;
    }
    if (additionalPictures?.length !== 0) {
      newCompletenessValue += 10;
    }
    if (testlang?.length > 0) {
      newCompletenessValue += 10;
    }
    if (profilePhoto !== undefined) {
      newCompletenessValue += 10;
    }
    if (profileTitle !== null || profileDescription !== null) {
      newCompletenessValue += 10;
    }
    if (industrySectors?.length > 0) {
      newCompletenessValue += 10;
    }

    setCompletenessValue(newCompletenessValue);
    onCompletenessChange(newCompletenessValue);
  }, []);

  return (
    <Modal
      active={active}
      onClose={deactivate}
      className='!w-[424px] !h-[auto] overflow-hidden p-[24px]'
    >
      <View>
        <View className='flex items-center p-0 mt-[16px]'>
          <Text className='text-[#323C58]  rubik font-normal text-[24px] leading-4 xl:leading-10'>
            Complete your profile
          </Text>
          <button
            onClick={deactivate}
            className='flex btn-no-hover items-center justify-end ml-auto'
          >
            <span className='material-icons align-middle text-500 '>close</span>
          </button>
        </View>

        <View className='flex flex-col mt-[20px]'>
          <View className='flex flex-row gap-[8px]'>
            <Text className='font-medium rubik text-[#383838] mt-[0px] mr-[0px]'>
              Profile completeness: {completenessValue}%
            </Text>
            <InfoTooltip text='Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile.' />
          </View>
          <Progress value={completenessValue} color='positive' className='mt-[8px]' />
        </View>
        <View className='flex flex-col gap-4 overflow-y-auto h-[420px] mt-[16px]'>
          {sortedValues.map(
            ({ value, heading, subHeading, link, activeTab }) => {
              return (
                <div key={heading} className=''>
                  {!value || value?.length === 0 ? (
                    <View className='flex flex-col justify-center items-start p-[16px] gap-4 self-stretch rounded-lg bg-[#F4F5F7]  '>
                      <View className='flex flex-row'>
                        <span className='material-icons-outlined text-[16px] text-[#323C58] p-[1.5px] mr-[3px]'>
                          cancel
                        </span>
                        <Text className='overflow-hidden text-[#383838] text-ellipsis rubik text-[16px] font-normal leading-[20px]'>
                          {heading}
                        </Text>
                      </View>
                      <Text className='overflow-hidden text-[#383838] text-ellipsis rubik text-[14px] font-normal leading-[20px]'>
                        {subHeading}
                      </Text>
                      <Button
                        variant='outline'
                        className='flex p-1 md:p-2 justify-center items-center gap-1 md:gap-2 rounded-md !bg-[#323C58] !text-[#ffff] text-[14px] rubik'
                        onClick={() =>
                          navigate(link, {
                            state: {
                              activeTab: activeTab,
                            },
                          })
                        }
                      >
                        Complete
                      </Button>
                    </View>
                  ) : (
                    <View className='flex flex-col justify-center items-start p-[16px] gap-4 self-stretch rounded-lg bg-[#F4F5F7]  '>
                      <View className='flex flex-row'>
                        <span className='material-icons-outlined text-[16px] text-[#05751F] p-[1.5px] mr-[3px]'>
                          check_circle
                        </span>
                        <Text className='overflow-hidden text-[#383838] text-ellipsis rubik text-[16px] font-normal leading-[20px]'>
                          {heading}
                        </Text>
                      </View>
                      <Text className='overflow-hidden text-[#383838] text-ellipsis rubik text-[14px] font-normal leading-[20px]'>
                        {subHeading}
                      </Text>
                    </View>
                  )}
                </div>
              );
            },
          )}
        </View>

        <Divider className='w-full h-[1px] mt-[16px]'></Divider>

        <Button
          variant='outline'
          icon={() => <span className='material-icons -mt-1'>clear</span>}
          onClick={deactivate}
          className='flex self-stretch w-[376px] h-[48px] p-3 md:p-4 justify-center items-center gap-2 md:gap-4 self-stretch rounded-lg border border-border-[#DFE2EA] !bg-[#ffff] mt-[10px] mx-auto'
        >
          Cancel
        </Button>
      </View>
    </Modal>
  );
};

export default CompleteProfileModal;
