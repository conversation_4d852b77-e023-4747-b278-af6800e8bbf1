import React, { useContext } from 'react';
import { Card, Text, Button, View } from 'reshaped';
import EditButton from 'src/components/common/EditButton';
import { useNavigate } from 'react-router-dom';
import NoDataProfile from '../NoDataProfile/NoDataProfile';
import { AppContext } from '../../../../context/AppContext';

const OperatorIndustrySelectors: React.FC = () => {
  const navigate = useNavigate();
  const { industrySectors } = useContext(AppContext);

  return (
    <Card className='xl:w-[424px] h-[auto] lg:mx-auto p-6'>
      <View className='flex flex-col gap-5'>
        <View className='flex items-center justify-between'>
          <Text className='text-center text-[#1A1A1A] rubik text-[16px] xl:font-medium xl:leading-5'>
            Industry sectors
          </Text>
          <EditButton
            size="small"
            onClick={() =>
              navigate('/operator-settings-profile-details', {
                state: {
                  activeTab: '0',
                },
              })
            }
          />
        </View>

        <View className='flex gap-2'>
          {industrySectors.length === 0 ? (
            <NoDataProfile />
          ) : (
            <div className=' flex flex-wrap gap-2'>
              {industrySectors.map((industrySector: any, index: any) => (
                <Button
                  key={index}
                  size='small'
                  rounded={true}
                  elevated={false}
                  className='px-[8px] py-[4px] border !bg-[#323C58] max-w-xs overflow-hidden truncate '
                >
                  <Text className='text-[#FFFFFF] rubik font-normal text-[12px]  leading-[20px]'>{industrySector}</Text>
                </Button>
              ))}
            </div>
          )}
        </View>
      </View>
    </Card>
  );
};

export default OperatorIndustrySelectors;
