// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, Text, View, Image } from 'reshaped';
import EditButton from 'src/components/common/EditButton';
import InfoTooltip from 'src/components/common/InfoTooltip';
import { useNavigate } from 'react-router-dom';
import { AppContext } from 'src/context/AppContext';
import NoDataProfile from '../NoDataProfile/NoDataProfile';

const OperatorBio: React.FC = () => {
  const navigate = useNavigate();

  const { profileTitle, profileDescription } = useContext(AppContext);
  return (
    <Card className='xl:w-[536px] xl:mx-auto  p-[24px] lg:w-[426px]'>
      <View className='flex items-center justify-between'>
        <div className='w-full flex justify-between'>
          <div className='flex gap-2 items-center'>
            <Text className='text-center text-black rubik text-[16px] xl:font-medium xl:leading-5'>Bio</Text>

            <InfoTooltip text='On your profile page, you have the opportunity to showcase your personality through a bio. The minimum number of characters allowed for your bio is 20. The maximum limit is set at 600 characters, encouraging concise and impactful self-introductions.' />
          </div>
          <EditButton
            onClick={() =>
              navigate('/operator-settings-profile-details', {
                state: {
                  activeTab: '2',
                },
              })
            }
          />
        </div>
      </View>
      {profileTitle !== null || profileDescription !== null ? (
        <View className='xl:w-[470px] mt-[20px] flex flex-col gap-2'>
          <Text className='text-neutral rubik text-lg font-medium leading-7 break-all ml-[4px] '>{profileTitle}</Text>
          <Text className='w-full text-light-gray rubik xl:text-body2 xl:font-normal xl:leading-5 '>
            {profileDescription}
          </Text>
        </View>
      ) : (
        <NoDataProfile />
      )}
    </Card>
  );
};

export default OperatorBio;
