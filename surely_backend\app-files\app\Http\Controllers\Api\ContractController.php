<?php

namespace App\Http\Controllers\Api;

use App\Events\NewMessageEvent;
use App\Http\Controllers\Controller;
use App\Http\Resources\ContractResource;
use App\Models\Chat;
use App\Models\Contract;
use App\Models\Invoice;
use App\Models\Job;
use App\Models\MobileUser;
use App\Models\StripeAccount;
use App\Notifications\ContractNotification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Stripe\StripeClient;

class ContractController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $contracts = Contract::where('client_id', auth()->id())->orWhere('operative_id', auth()->id());
        if ($request->filled('chat_id')) {
            $contracts->where('chat_id', $request->get('chat_id'));
        }
        $contracts->orderBy('created_at', 'DESC');
        $contracts = ContractResource::collection($contracts->get());
        return response()->json(['data' => $contracts]);
    }

    public function payments(Request $request): JsonResponse
    {
        $contracts = Contract::where('client_id', auth()->id());

        $contracts->orderBy('updated_at', 'DESC')
            ->where('payment_status', '!=', Contract::PAYMENT_STATUS_PAID)
            ->where('shifts_status', '!=', Contract::SHIFT_STATUS_NEED_REVIEW)
            ->where('status', '!=', Contract::canceled)
            ->where('status', '!=', Contract::rejected)
            ->where('status', '!=', Contract::in_progress)
            ->where('shifts_status', '!=', Contract::SHIFT_STATUS_PENDING);

        if ($request->filled('chat_id')) {
            $contracts->where('chat_id', $request->get('chat_id'));
        }

        $contracts->orderBy('updated_at', 'DESC');
        $contracts = ContractResource::collection($contracts->get());
        return response()->json(['data' => $contracts]);
    }

    public function storeFromChat(Request $request, $chatId): JsonResponse
    {
        $chat = Chat::find($chatId);

        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Chat session has expired!',
                'data' => []
            ],401);
        }

        if($chat->contract_id != null){
            return response()->json([
                'error' => true,
                'message' => 'Chat already has a contract!',
                'data' => []
            ]);
        }

        $request['client_id'] = $chat->sender_id;
        $request['operative_id'] = $chat->receiver_id;
        $request['chat_id'] = $chat->id;

        $request['escrow_status'] = Contract::ESCROW_STATUS_PENDING;
        $request['payment_status'] = Contract::PAYMENT_STATUS_PENDING;
        $request['status'] = Contract::pending;
        $request['shifts_status'] = Contract::SHIFT_STATUS_PENDING;

        $contract = Contract::create($request->all());
        if(! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot created!',
                'data' => []
            ]);
        }

        $request['contract_id'] = $contract->id;
        $request['sender_id'] = $chat->sender_id;
        $request['receiver_id'] = $chat->receiver_id;

        $chat->update($request->only('contract_id'));

        $message = $chat->writeMessage($request);

        broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

        return response()->json([
            'error' => false,
            'message' => 'Contract created successfully!',
            'data' => $contract
        ]);
    }

    public function store(Request $request) {
        $request->validate([
            'client_id' => 'required',
            'operative_id' => 'required',
            'job_id' => 'required',
            'chat_id' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
            'start_time' => 'required',
            'end_time' => 'required',
            'payment_type' => 'required',
            'payment_amount' => 'required',
            'payment_status' => 'required',
            'payment_date' => 'required',
            'payment_method' => 'required',
            'payment_reference' => 'required',
            'payment_description' => 'required',
            'payment_image' => 'required',
            'status' => 'required',
        ]);
        $chatId = $request->get('chat_id');
        $chat = Chat::find($chatId);
        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Chat session has expired!',
                'data' => []
            ], 401);
        }
        $request['client_id'] = $chat->sender_id;
        $request['operative_id'] = $chat->receiver_id;
        $request['chat_id'] = $chat->id;
        $request['escrow_status'] = Contract::ESCROW_STATUS_PENDING;
        $request['payment_status'] = Contract::PAYMENT_STATUS_PENDING;
        $request['status'] = Contract::pending;
        $request['shifts_status'] = Contract::SHIFT_STATUS_PENDING;

        $job = Job::create($request->all());
        if (! $job) {
            return response()->json([
                'error' => true,
                'message' => 'Job cannot created!',
                'data' => []
            ]);
        }

        $request['job_id'] = $job->id;
        $request['escrow_status'] = Contract::ESCROW_STATUS_PENDING;
        $request['payment_status'] = Contract::PAYMENT_STATUS_PENDING;
        $request['status'] = Contract::pending;
        $request['shifts_status'] = Contract::SHIFT_STATUS_PENDING;

        $contract = Contract::create($request->all());

        return response()->json([
            'error' => false,
            'message' => 'Contract created successfully!',
            'data' => $request->all()
        ]);
    }

    public function show($contractId): JsonResponse
    {
        $contract = Contract::find($contractId);

        if (! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found #'.$contractId.'!',
                'data' => [],
            ]);
        }

        if($contract->client_id != auth()->id() && $contract->operative_id != auth()->id()){
            return response()->json([
                'error' => true,
                'message' => 'You are not authorized to view this contract!',
                'data' => [],
            ]);
        }
        //don't touch it TIME WASTED 5h 30m add if u spend too

        if($contract->escrow_status == Contract::ESCROW_STATUS_PAID
            && $contract->shifts_status == Contract::SHIFT_STATUS_ACCEPTED
//            $contract->status == Contract::in_progress &&
        //uncomment for optimization
        ) {
            if($contract->getEndDate() < now()->format('Y-m-d H:i:s') && $contract->payment_status == Contract::PAYMENT_STATUS_PAID) {
                $contract->update(['shifts_status' => Contract::SHIFT_STATUS_NEED_REVIEW, 'status' => Contract::complete]);
            }
        }
        if($contract->escrow_status == Contract::ESCROW_STATUS_PENDING){
            // Calculate base subtotal (Rate × Hours) - this should never be modified
            $originalSubTotal = PaymentController::calculateTotalPayment($contract->date_range, $contract->hourly_rate);

            // Calculate Emergency Hire Fee (5% of original subtotal)
            $emergency_hire = $contract->job?->is_emergency_hire ?? false;
            $emergency_hire_fee_amount = 0;
            $emergency_hire_fee_rate = 0;

            if($emergency_hire){
                $emergency_hire_fee_rate = Contract::EMERGENCY_HIRE_FEE_RATE;
                $emergency_hire_fee_amount = $originalSubTotal * (Contract::EMERGENCY_HIRE_FEE_RATE / 100);
            }

            // Calculate Operative VAT (20% of original subtotal, if applicable)
            $operatorVatRate = MobileUser::find($contract->operative_id)->operator_vat ?? 0;
            $operatorVatAmount = 0;
            if($operatorVatRate > 0) {
                $operatorVatAmount = $originalSubTotal * ($operatorVatRate / 100);
            }

            // Calculate Surely Fee base: Original Subtotal + Operative VAT + Emergency Hire
            $surelyFeeBase = $originalSubTotal + $operatorVatAmount + $emergency_hire_fee_amount;

            $appFee = 0;
            $appFeeVat = 0;
            $appFeeRate = 0;
            $appVatRate = 0;
            $transactionFeeAmount = 0;
            $transactionFeeRate = 0;

            // Check if client is new (created within last month) - use transaction fee model
            if($contract->client->created_at->format('Y-m-d H:i:s') > now()->subMonth()->format('Y-m-d H:i:s')) {
                // For new clients: Use transaction fee instead of application fee
                $transactionFeeAmount = $surelyFeeBase * (Contract::TRANSACTION_FEE_RATE / 100);
                $transactionFeeRate = Contract::TRANSACTION_FEE_RATE;
                $appVatRate = Contract::APPLICATION_VAT_RATE;
                $appFeeVat = $transactionFeeAmount * ($appVatRate / 100);
            } else {
                // For existing clients: Use standard application fee model
                $appFeeRate = Contract::APPLICATION_FEE_RATE;
                $appFee = $surelyFeeBase * ($appFeeRate / 100);
                $appVatRate = Contract::APPLICATION_VAT_RATE;
                $appFeeVat = $appFee * ($appVatRate / 100);
            }

            // Calculate total amount: Original Subtotal + All Fees
            $totalAmount = $originalSubTotal + $operatorVatAmount + $emergency_hire_fee_amount + $appFee + $transactionFeeAmount + $appFeeVat;

            $escrowAmount = $totalAmount * (MobileUser::find($contract->client_id)->getEscrowDeposit() / 100);

            $contract->update([
                'application_vat_rate' => $appVatRate,
                'application_vat_amount' => $appFeeVat,
                'application_fee_amount' => $appFee,
                'application_fee_rate' => $appFeeRate,
                'sub_total' => $originalSubTotal, // Keep original subtotal unchanged
                'escrow_amount' => $escrowAmount,
                'total_amount' => $totalAmount,
                'operator_vat_amount' => $operatorVatAmount,
                'operator_vat_rate' => $operatorVatRate,
                'escrow_rate' => MobileUser::find($contract->client_id)->getEscrowDeposit(),
                'emergency_hire_fee_amount' => $emergency_hire_fee_amount,
                'emergency_hire_fee_rate' => $emergency_hire_fee_rate,
                'payment_transaction_fee_rate' => $transactionFeeRate,
                'payment_transaction_fee_amount' => $transactionFeeAmount,
            ]);
        }

        return !$contract
            ? response()->json(['error' => true, 'message' => 'No contract found #'.$contractId.'!', 'data' => []])
            :response()->json([
                'error' => false,
                'message' => 'Data retrieved successfully!',
                'data' => new ContractResource($contract),
            ]);
    }

    public function update(Request $request, $contractId): JsonResponse
    {
        $contract = Contract::find($contractId);

        if (! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found #'.$contractId.'!',
                'data' => [],
            ]);
        }

        if (! $contract->update($request->all())) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot be updated!',
                'data' => [],
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Contract updated successfully!',
            'data' => $contract,
        ]);
    }

    public function updateShifts(Request $request, $contractId): JsonResponse
    {
        $contract = Contract::find($contractId);
        if (! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found #'.$contractId.'!',
                'data' => [],
            ]);
        }
        if ($contract->status == Contract::canceled) {
            return response()->json([
                'error' => true,
                'message' => 'Contract canceled!',
                'data' => [],
            ]);
        }
        if ($contract->status == Contract::complete) {
            return response()->json([
                'error' => true,
                'message' => 'Contract completed!',
                'data' => [],
            ]);
        }
        if ($contract->status == Contract::rejected) {
            return response()->json([
                'error' => true,
                'message' => 'Contract rejected!',
                'data' => [],
            ]);
        }
        if ($contract->status == Contract::in_progress) {
            return response()->json([
                'error' => true,
                'message' => 'Contract in progress!',
                'data' => [],
            ]);
        }
        if($contract->escrow_status != Contract::ESCROW_STATUS_PENDING){
            return response()->json([
                'error' => true,
                'message' => 'Escrow payment already done!',
                'data' => [],
            ]);
        }
        $contract->update(['shifts_status' => Contract::SHIFT_STATUS_PENDING]);

        $chat = Chat::find($contract->chat_id);
        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Chat session has expired!',
                'data' => []
            ],401);
        }

        if (! $contract->update($request->all())) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot updated!',
                'data' => [],
            ]);
        }

        $request['job_id'] = $contract->job_id;
        $request['contract_id'] = $contract->id;
        $request['sender_id'] = auth()->id();
        $request['receiver_id'] = auth()->id() === $chat->receiver_id ? $chat->sender_id : $chat->receiver_id;
//        $request['type'] = "shift_update_request";
//        $request['message'] = "contract_shifts_updated";

        $message = $chat->writeMessage($request);
        broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

        return response()->json([
            'error' => false,
            'message' => 'Contract shifts updated successfully!',
            'data' => new ContractResource($contract),
        ]);
    }

    public function changeStatus(Request $request, $chatId, $contractId): JsonResponse
    {
        $chat = Chat::find($chatId);

        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Chat session has expired!',
                'data' => []
            ],401);
        }

        $contract = Contract::find($contractId);

        if (! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found #'.$contractId.'!',
                'data' => [],
            ]);
        }

        if($contract->client_id != auth()->id() && $contract->operative_id != auth()->id()){
            return response()->json([
                'error' => true,
                'message' => 'You are not authorized to view this contract!',
                'data' => [],
            ]);
        }

        if ($request->status == 'cancel') {
            $result = $this->cancel($contract);
            if($result){
                return $result;
            }
            $contract->reason = $request->reason;
        }

        else if ($request->status == 'accept-shifts') {
            $result = $this->acceptShifts($contract);
            if($result){
                return $result;
            }
        }

        else if ($request->status == 'confirm-shifts') {
            $result = $this->confirmShifts($contract);
            if($result){
                return $result;
            }
        }

        else if($request->status == 'complete-shifts') {
            $result = $this->completeShifts($contract);
            if($result){
                return $result;
            }
        }

        else if($request->status == 'reject') {
            $result = $this->reject($contract);
            if($result){
                return $result;
            }
            $contract->reason = $request->reason;
        }

        else {
            if (!$contract->save()) {
                return response()->json([
                    'error' => true,
                    'message' => 'Contract cannot updated!',
                    'data' => [],
                ]);
            }
        }

        $request['job_id'] = $contract->job_id ?? null;
        $request['contract_id'] = $contract->id;
        $request['sender_id'] = auth()->id();
        $request['receiver_id'] = auth()->id() == $chat->receiver_id ? $chat->sender_id : $chat->receiver_id;

        $message = $chat->writeMessage($request);
        $operator = MobileUser::find($request['receiver_id']);

        if ($operator->notification('contract_status_mail')) {
            $operator->notify(new ContractNotification($operator->name, $contract->id));
        }

        if ($operator->notification('contract_status_alert')) {
            broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();
        }

        return response()->json([
            'error' => false,
            'message' => 'Contract updated successfully!',
            'data' => $contract,
        ]);
    }

    private function reject($contract)
    {
        if($contract->status == Contract::canceled){
            return response()->json([
                'error' => true,
                'message' => 'Contract canceled!',
                'data' => [],
            ]);
        }

        if ($contract->payment_status == Contract::PAYMENT_STATUS_PAID) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot be canceled because it is already paid!',
                'data' => [],
            ]);
        }

        $contract->update(['status' => Contract::rejected]);

        return $contract;
    }

    private function completeShifts($contract)
    {
        if($contract->status == Contract::canceled){
            return response()->json([
                'error' => true,
                'message' => 'Contract canceled!',
                'data' => [],
            ]);
        }

        if ($contract->payment_status == Contract::PAYMENT_STATUS_PAID) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot be canceled because it is already paid!',
                'data' => [],
            ]);
        }

        if($contract->shifts_status != Contract::SHIFT_STATUS_NEED_REVIEW){
            return response()->json([
                'error' => true,
                'message' => 'Shift not ended yet!',
                'data' => [],
            ]);
        }

        $contract->update(['shifts_status' => Contract::SHIFT_STATUS_COMPLETED]);

        return null;
    }

    private function acceptShifts($contract)
    {
        if($contract->status == Contract::canceled){
            return response()->json([
                'error' => true,
                'message' => 'Contract already canceled!',
                'data' => [],
            ]);
        }

        $contract->update([
            'shifts_status' => Contract::SHIFT_STATUS_ACCEPTED,
            'status' => Contract::in_progress,
            'shifts_accepted_at' => now(),
        ]);

       return null;
    }

    private function confirmShifts($contract)
    {
        if($contract->status == Contract::canceled){
            return response()->json([
                'error' => true,
                'message' => 'Contract already canceled!',
                'data' => [],
            ]);
        }

        if ($contract->payment_status == Contract::PAYMENT_STATUS_PAID) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot be canceled because it is already paid!',
                'data' => [],
            ]);
        }

        if($contract->shifts_status != Contract::SHIFT_STATUS_PENDING){
            return response()->json([
                'error' => true,
                'message' => 'Shifts already confirmed!',
                'data' => [],
            ]);
        }

        $contract->update(['shifts_status' => Contract::SHIFT_STATUS_CONFIRMED]);

        return null;
    }

    private function cancel($contract)
    {
        if($contract->status == Contract::canceled){
            return response()->json([
                'error' => true,
                'message' => 'Contract already canceled!',
                'data' => [],
            ]);
        }

        if($contract->status == Contract::complete){
            return response()->json([
                'error' => true,
                'message' => 'Contract already completed!',
                'data' => [],
            ]);
        }

        if ($contract->payment_status == Contract::PAYMENT_STATUS_PAID) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot be canceled because it is already paid!',
                'data' => [],
            ]);
        }

        if ($contract->payment_status == Contract::PAYMENT_STATUS_REFUNDED) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot be canceled because it is already refunded!',
                'data' => [],
            ]);
        }

        if($contract->escrow_status == Contract::ESCROW_STATUS_PAID){
            $invoice = Invoice::where('contract_id', $contract->id)->where('type', 'escrow')->first();

            $amountToRefund = $contract->sub_total * ($contract->escrow_rate / 100);
            if($contract->operator_vat_amount > 1){
                $amountToRefund += $contract->operator_vat_amount * ($contract->escrow_rate / 100);
            }
           if($contract->escrow_status == Contract::ESCROW_STATUS_PAID){
               $stripe = new StripeClient(StripeAccount::first()->secret);

               try {
                   $stripe->refunds->create([
                        'payment_intent' => $invoice->stripe_payment_intent_id,
                        'amount' => $amountToRefund * 100,
                   ]);

                   $contract->update(['escrow_status' => Contract::ESCROW_STATUS_REFUNDED, 'status' => Contract::canceled]);
                   $invoice->update(['payment_status' => Contract::PAYMENT_STATUS_REFUNDED]);
               } catch (\Exception $exception){
                   return response()->json([
                       'error' => true,
                       'message' => $exception->getMessage(),
                       'data' => [],
                   ]);
               }

               return response()->json([
                   'error' => false, //if refund is successful, the error should be false
                   'message' => 'Refund successfully!',
                   'data' => [],
               ]);
           }
        }

        $contract->update(['status' => Contract::canceled]);

        return null;
    }

    public function myInvoices()
    {
        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => Invoice::with('contract')->where('client_id', auth()->id())->orWhere('operative_id', auth()->id())->get(),
        ]);
    }

    public function contractInvoices(Request $request, $id)
    {
        $invoice = Invoice::with('contract')->where('contract_id',$id)->where('client_id', auth()->id())->orWhere('operative_id', auth()->id());

        if($request->filled('type')){
            $invoice = $invoice->where('type', $request->get('type'));
        }
        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => $invoice->get(),
        ]);
    }
    public function contractInvoicesList(Request $request)
    {
        if (auth()->user()->account_type == MobileUser::business) {
            $accountType = 'client_id';
        } elseif (auth()->user()->account_type == MobileUser::freelancer) {
            $accountType = 'operative_id';
        } else {
            return response()->json([
                'error' => true,
                'message' => 'Unauthorized to process request!'
            ]);
        }

        $contracts = Contract::where('payment_status', Contract::PAYMENT_STATUS_PAID)->where($accountType, auth()->id())->orderBy('created_at','DESC')->get();

        if ($contracts->isEmpty()) {
            return response()->json([
                'error' => true,
                'message' => 'No contracts found!',
                'data' => [],
            ]);
        }

        $contractsList = [];
        foreach ($contracts as $contract) {
                $invoiceEscrow = Invoice::where('contract_id', $contract->id)->where('type', 'escrow')->first();

                if (!$invoiceEscrow) {
                    return 'Escrow invoice not found!';
                }

                $invoiceOutstanding = Invoice::where('contract_id', $contract->id)->where('type', 'payment')->first();

                $contractsList[] = [
                    'contract' => $contract,
                    'client' => $contract->client,
                    'operative' => $contract->operative,
                    'invoiceEscrow' => $invoiceEscrow,
                    'invoiceOutstanding' => $invoiceOutstanding
                ];
            }

            return response()->json([
                'error' => false,
                'message' => 'Data retrieved successfully!',
                'data' => $contractsList,
            ]);
    }

    public function contractInvoice(Request $request, $id)
    {
        $contract = Contract::find($id);

        if($contract->client_id != auth()->id() && $contract->operative_id != auth()->id()){
            return response()->json([
                'error' => true,
                'message' => 'You are not authorized to view this contract!',
                'data' => [],
            ]);
        }

        if(!$contract){
            return 'Contract not found!';
        }

        $invoiceEscrow = Invoice::where('contract_id', $contract->id)->where('type', 'escrow')->first();
        if(!$invoiceEscrow){
            return 'Escrow invoice not found!';
        }

        $invoiceOutstanding = Invoice::where('contract_id', $contract->id)->where('type', 'payment')->first();

        $view = 'invoices.invoices';

        //if escrow invoice fully seperated
//        if($request->filled('type')){
//            if($request->get('type') == 'escrow'){
//                $view = 'invoices.escrowInvoice';
//            }
//            if($request->get('type') == 'payment'){
//                $view = 'invoices.outstandingInvoice';
//            }
//        }
        $data = [
            'contract' => $contract,
            'client' => $contract->client,
            'operative' => $contract->operative,
            'invoiceEscrow' => $invoiceEscrow,
            'invoiceOutstanding' => $invoiceOutstanding,
        ];

//
//        $pdf = Pdf::loadView($view, $data);
//        return $pdf->stream('invoice.pdf');
        $dueDate = clone $invoiceEscrow->created_at;

        $paymentTerms = $contract->payment_terms;

        if($paymentTerms) {
            $dueDate->modify('+' . $paymentTerms . ' days');
        }else{
            $dueDate->modify('+10 days');
        }
        $dueDateFormatted = $dueDate->format('d-m-Y');

        return response()->json([
            'contract' => array_merge($contract->toArray(),[
                'shift_hours' => $contract->shiftHours(),
                'issue_date' => $invoiceEscrow->created_at->format('d-m-Y'),
                'due_date' => $dueDateFormatted
            ]),
            'client' => $contract->client,
            'operative' => $contract->operative,
            'invoiceEscrow' => $invoiceEscrow,
            'invoiceOutstanding' => $invoiceOutstanding
        ]);
    }
}
